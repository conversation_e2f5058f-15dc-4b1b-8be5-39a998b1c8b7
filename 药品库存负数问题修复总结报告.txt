===============================================================================
                        药品库存负数问题修复总结报告
===============================================================================
创建日期：2025-01-17
修复范围：处方发药站库存管理逻辑
问题级别：严重 - 导致药品库存为负数

===============================================================================
一、问题背景分析
===============================================================================

1.1 问题现象
-----------
用户发现DRUG_STOCK表中存在大量负库存记录，例如：
- 恩格列净片：库存 -1.00盒
- 秦艽：库存 -56.00克  
- 肉豆蔻：库存 -30.00克
- 附片：库存 -60.00克
- 大叶茜草：库存 -40.00克
等多个药品出现负库存情况。

1.2 问题影响
-----------
- 库存数据不准确，影响药品管理
- 可能导致实际无库存但系统显示有库存的情况
- 影响药品采购和配送决策
- 违反业务逻辑（库存不应为负数）

===============================================================================
二、根本原因分析
===============================================================================

2.1 最危险的问题：直接设置库存值
-----------------------------
文件：FrmSurgeryMorePresdispDeliver.cs
位置：UpdateDrugStock方法

【修复前代码】：
string sql = "UPDATE DRUG_STOCK SET QUANTITY='" + quantity + "',LAST_UPDATETIME=SYSDATE 
WHERE STORAGE = '" + storagecode + "' AND SUPPLY_INDICATOR = 1 AND QUANTITY > 0 
and drug_code = '" + drugcode + "' and drug_spec = '" + drugspec + "' 
and package_spec = '" + packagespec + "' and firm_id = '" + firmid + "' 
and BATCH_NO='" + batchno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";

【问题分析】：
- 使用 QUANTITY='{quantity} 直接设置库存为指定值
- 这是最危险的操作，完全忽略当前库存
- 可能导致严重的库存数据错误

2.2 库存扣减缺少安全检查
---------------------
文件：FrmOutpOnePresdispDeliver.cs, FrmInHospitalMorePresdispDeliver.cs
位置：UpdateDrugStock方法

【修复前代码】：
string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE 
WHERE STORAGE = '" + storagecode + "' AND QUANTITY > 0 and drug_code = '" + drugcode + "'";

【问题分析】：
- QUANTITY > 0 只是WHERE条件，不能防止扣减后变成负数
- 缺少库存充足性检查
- 可能在并发情况下产生负库存

2.3 统一库存验证参数问题
---------------------
文件：UnifiedStockValidator.cs, FrmInHospitalMorePresdispDeliver.cs

【问题分析】：
- 住院发药使用this.DeptCode（当前科室）而不是处方中的药房代码
- 可能导致参数查询失败，统一验证被跳过
- 预扣库存验证被临时禁用

===============================================================================
三、修复方案详细说明
===============================================================================

3.1 修复危险的直接设置操作
------------------------
文件：FrmSurgeryMorePresdispDeliver.cs
修改位置：第1443-1460行

【修复后代码】：
// 2025-01-17 修复：改为扣减操作，并添加库存充足性检查
// 原代码：QUANTITY='{quantity}' 直接设置库存值（危险操作）
// 修复后：QUANTITY = QUANTITY - {quantity} 扣减库存，并检查扣减后不为负数
string sql = "UPDATE DRUG_STOCK SET QUANTITY = QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE " +
    "WHERE STORAGE = '" + storagecode + "' AND SUPPLY_INDICATOR = 1 " +
    "AND QUANTITY >= " + quantity + " " + // 确保库存充足
    "AND drug_code = '" + drugcode + "' and drug_spec = '" + drugspec + "' " +
    "and package_spec = '" + packagespec + "' and firm_id = '" + firmid + "' " +
    "and BATCH_NO='" + batchno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";

【修复效果】：
- 改为安全的扣减操作
- 添加库存充足性检查：QUANTITY >= quantity
- 防止产生负库存

3.2 修复门诊发药库存扣减安全检查
-----------------------------
文件：FrmOutpOnePresdispDeliver.cs
修改位置：第2325-2353行

【修复后代码】：
// 2025-01-17 修复：确保扣减后库存不为负数
// 原条件：QUANTITY > 0 只是查询条件，不能防止扣减后变负
// 修复后：QUANTITY >= quantity 确保库存充足才能扣减
string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE " +
    "WHERE STORAGE = '" + storagecode + "' AND QUANTITY >= " + quantity + " and drug_code = '" + drugcode + "' " +
    "and drug_spec = '" + drugspec + "' and package_spec = '" + packagespec + "' " +
    "and firm_id = '" + firmid + "'";

【修复效果】：
- 确保库存充足才能扣减
- 防止并发情况下的负库存

3.3 修复住院发药库存扣减安全检查
-----------------------------
文件：FrmInHospitalMorePresdispDeliver.cs
修改位置：第1935-1948行

【修复后代码】：
// 2025-01-17 修复：确保扣减后库存不为负数
// 原条件：QUANTITY > 0 只是查询条件，不能防止扣减后变负
// 修复后：QUANTITY >= quantity 确保库存充足才能扣减
// 同时修复：quantity参数不应该用引号包围（数值类型）
string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE 
WHERE STORAGE = '" + storagecode + "' AND QUANTITY >= " + quantity + " and drug_code = '" + drugcode + "' 
and drug_spec = '" + drugspec + "' and package_spec = '" + packagespec + "' and firm_id = '" + firmid + "' 
and BATCH_NO='" + batchno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";

【修复效果】：
- 库存充足性检查：QUANTITY > 0 → QUANTITY >= quantity
- 修复数值类型错误：去掉quantity参数的引号

3.4 修复住院发药统一库存验证药房代码问题
-----------------------------------
文件：FrmInHospitalMorePresdispDeliver.cs
修改位置：第552-566行

【修复前代码】：
StockValidationResult validationResult = UnifiedStockValidator.ValidateStock(
    drugCode, drugName, packageSpec, firmId, this.DeptCode, detailcount1, units);

【修复后代码】：
// 2025-01-17 修复：使用处方中的药房代码，而不是当前科室代码
string dispensary = masterdr != null && masterdr["DISPENSARY"] != DBNull.Value 
    ? masterdr["DISPENSARY"].ToString() 
    : this.DeptCode; // 如果处方中没有药房代码，则使用当前科室代码作为备选

StockValidationResult validationResult = UnifiedStockValidator.ValidateStock(
    drugCode, drugName, packageSpec, firmId, dispensary, detailcount1, units);

【修复效果】：
- 使用正确的药房代码进行参数查询
- 与门诊发药逻辑保持一致
- 提高统一库存验证的准确性

3.5 改进统一库存验证调试功能
-------------------------
文件：UnifiedStockValidator.cs
修改位置：第17-56行，第53-74行

【新增功能】：
- 添加TestParameterRetrieval测试方法
- 改进参数查询调试日志
- 提供详细的参数查询信息

【修复效果】：
- 便于排查参数获取问题
- 提供详细的调试信息
- 帮助确认统一验证是否正常工作

===============================================================================
四、修复效果预期
===============================================================================

4.1 防护措施
-----------
1. 所有库存扣减操作都有安全检查：QUANTITY >= 扣减数量
2. 危险的直接设置操作已修复为安全的扣减操作
3. 统一库存验证使用正确的药房代码
4. 详细的调试日志便于问题追踪

4.2 预期效果
-----------
1. 不会再产生新的负库存记录
2. 统一库存验证能正常工作
3. 门诊发药和住院发药逻辑一致
4. 库存管理更加安全可靠

===============================================================================
五、测试验证方案
===============================================================================

5.1 功能测试
-----------
1. 重启处方发药站程序
2. 进行门诊发药操作测试
3. 进行住院发药操作测试
4. 观察是否还会产生负库存

5.2 日志验证
-----------
查看日志文件：../Client/LOG/exLOG/处方发药站_库存验证_YYYYMMDD.log
关注以下信息：
- [TestParameterRetrieval] 参数获取测试结果
- [ValidateStock] 统一库存验证执行情况
- dispensary 药房代码的实际值
- 参数查询结果

5.3 数据库监控
-------------
定期执行以下SQL监控负库存情况：
SELECT COUNT(*) as 负库存记录数, MIN(QUANTITY) as 最小库存值
FROM DRUG_STOCK WHERE QUANTITY < 0;

===============================================================================
六、重要注意事项
===============================================================================

6.1 部署注意事项
--------------
1. 建议先在测试环境验证所有修改
2. 部署前备份相关数据和程序文件
3. 部署后密切监控系统运行情况
4. 关注用户反馈和系统日志

6.2 参数配置确认
--------------
确认以下参数配置正确：
APP_NAME='PRESDISP'
DEPT_CODE='*' 
EMP_NO='*'
PARAMETER_NAME='ENABLE_UNIFIED_STOCK_VALIDATION'
PARAMETER_VALUE='1'
HIS_UNIT_CODE='45038900950011711A6001'

6.3 回退方案
-----------
如果出现问题，可以：
1. 恢复修改前的程序文件
2. 重启处方发药站程序
3. 监控系统恢复情况

===============================================================================
七、技术支持联系方式
===============================================================================

如遇问题，请检查：
1. 程序编译是否成功
2. 参数配置是否正确
3. 系统日志中的错误信息
4. 数据库连接是否正常

===============================================================================
八、详细代码对比
===============================================================================

8.1 手术发药模块修复对比
---------------------
文件：TjhisPlatSource\Tjhis_Presdisp_Station\View\FrmSurgeryMorePresdispDeliver.cs

【修复前】：
private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode,
    string drugspec, string packagespec, string firmid, string batchno)
{
    string sql = "UPDATE DRUG_STOCK SET QUANTITY='" + quantity + "',LAST_UPDATETIME=SYSDATE
    WHERE STORAGE = '" + storagecode + "' AND SUPPLY_INDICATOR = 1 AND QUANTITY > 0
    and drug_code = '" + drugcode + "' and drug_spec = '" + drugspec + "'
    and package_spec = '" + packagespec + "' and firm_id = '" + firmid + "'
    and BATCH_NO='" + batchno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
    return sql;
}

【修复后】：
private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode,
    string drugspec, string packagespec, string firmid, string batchno)
{
    // 2025-01-17 修复：改为扣减操作，并添加库存充足性检查
    // 原代码：QUANTITY='{quantity}' 直接设置库存值（危险操作）
    // 修复后：QUANTITY = QUANTITY - {quantity} 扣减库存，并检查扣减后不为负数
    string sql = "UPDATE DRUG_STOCK SET QUANTITY = QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE " +
        "WHERE STORAGE = '" + storagecode + "' AND SUPPLY_INDICATOR = 1 " +
        "AND QUANTITY >= " + quantity + " " + // 确保库存充足
        "AND drug_code = '" + drugcode + "' and drug_spec = '" + drugspec + "' " +
        "and package_spec = '" + packagespec + "' and firm_id = '" + firmid + "' " +
        "and BATCH_NO='" + batchno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
    return sql;
}

8.2 门诊发药模块修复对比
---------------------
文件：TjhisPlatSource\Tjhis_Presdisp_Station\View\FrmOutpOnePresdispDeliver.cs

【修复前】：
private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode,
    string drugspec, string packagespec, string firmid, string batchno, string batchcode,
    string retailprice)
{
    string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE " +
        "WHERE STORAGE = '" + storagecode + "' AND QUANTITY > 0 and drug_code = '" + drugcode + "' " +
        "and drug_spec = '" + drugspec + "' and package_spec = '" + packagespec + "' " +
        "and firm_id = '" + firmid + "'";
    // ... 其他条件
    return sql;
}

【修复后】：
private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode,
    string drugspec, string packagespec, string firmid, string batchno, string batchcode,
    string retailprice)
{
    // 2025-01-17 修复：确保扣减后库存不为负数
    // 原条件：QUANTITY > 0 只是查询条件，不能防止扣减后变负
    // 修复后：QUANTITY >= quantity 确保库存充足才能扣减
    string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE " +
        "WHERE STORAGE = '" + storagecode + "' AND QUANTITY >= " + quantity + " and drug_code = '" + drugcode + "' " +
        "and drug_spec = '" + drugspec + "' and package_spec = '" + packagespec + "' " +
        "and firm_id = '" + firmid + "'";
    // ... 其他条件
    return sql;
}

8.3 住院发药模块修复对比
---------------------
文件：TjhisPlatSource\Tjhis_Presdisp_Station\View\FrmInHospitalMorePresdispDeliver.cs

【修复前】：
private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode,
    string drugspec, string packagespec, string firmid, string batchno)
{
    string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - '" + quantity + "',LAST_UPDATETIME=SYSDATE
    WHERE STORAGE = '" + storagecode + "' AND QUANTITY > 0 and drug_code = '" + drugcode + "'
    and drug_spec = '" + drugspec + "' and package_spec = '" + packagespec + "'
    and firm_id = '" + firmid + "' and BATCH_NO='" + batchno + "'
    and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
    return sql;
}

【修复后】：
private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode,
    string drugspec, string packagespec, string firmid, string batchno)
{
    // 2025-01-17 修复：确保扣减后库存不为负数
    // 原条件：QUANTITY > 0 只是查询条件，不能防止扣减后变负
    // 修复后：QUANTITY >= quantity 确保库存充足才能扣减
    // 同时修复：quantity参数不应该用引号包围（数值类型）
    string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE
    WHERE STORAGE = '" + storagecode + "' AND QUANTITY >= " + quantity + " and drug_code = '" + drugcode + "'
    and drug_spec = '" + drugspec + "' and package_spec = '" + packagespec + "'
    and firm_id = '" + firmid + "' and BATCH_NO='" + batchno + "'
    and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
    return sql;
}

===============================================================================
九、系统参数查询机制说明
===============================================================================

9.1 参数查询优先级
----------------
SystemParm.GetParameterValue方法按以下优先级查询：

1. 个人级配置：
   APP_NAME='PRESDISP' AND DEPT_CODE='{具体药房}' AND EMP_NO='{具体用户}'

2. 科室级配置：
   APP_NAME='PRESDISP' AND DEPT_CODE='{具体药房}' AND EMP_NO='*'

3. 模块级配置：
   APP_NAME='PRESDISP' AND DEPT_CODE='*' AND EMP_NO='*'

9.2 当前参数配置
--------------
APP_NAME='PRESDISP'
DEPT_CODE='*'
EMP_NO='*'
PARAMETER_NAME='ENABLE_UNIFIED_STOCK_VALIDATION'
PARAMETER_VALUE='1'
HIS_UNIT_CODE='45038900950011711A6001'

此配置表示：所有科室的所有用户都启用统一库存验证

9.3 药房代码使用对比
-----------------
【门诊发药】：
- 使用：prescDispensary（处方指定的药房代码）✅
- 来源：处方数据中的药房字段
- 正确性：高

【住院发药（修复前）】：
- 使用：this.DeptCode（当前登录科室代码）❌
- 来源：用户登录信息
- 问题：可能不是药房代码

【住院发药（修复后）】：
- 使用：masterdr["DISPENSARY"]（处方指定的药房代码）✅
- 来源：处方主记录中的药房字段
- 备选：this.DeptCode（如果处方中没有药房代码）
- 正确性：高

===============================================================================
十、风险评估与控制
===============================================================================

10.1 修复风险评估
---------------
【低风险修改】：
- 库存扣减安全检查：QUANTITY > 0 → QUANTITY >= quantity
- 影响：只会让库存检查更严格，不会影响正常业务

【中风险修改】：
- 住院发药药房代码：this.DeptCode → masterdr["DISPENSARY"]
- 影响：可能影响参数查询结果，但有备选方案

【高风险修改】：
- 手术发药直接设置：QUANTITY=quantity → QUANTITY = QUANTITY - quantity
- 影响：改变了库存更新逻辑，但修复了严重错误

10.2 风险控制措施
---------------
1. 所有修改都添加了详细注释说明修复原因
2. 保留了原有的业务逻辑流程
3. 添加了备选方案（住院发药药房代码）
4. 提供了详细的调试日志
5. 建议在测试环境先验证

10.3 回退策略
-----------
如果出现问题，可以按以下步骤回退：

1. 立即回退：
   - 恢复修改前的程序文件
   - 重启处方发药站程序

2. 部分回退：
   - 如果只是参数问题，可以修改数据库参数：
     UPDATE APP_CONFIGER_PARAMETER SET PARAMETER_VALUE = '0'
     WHERE PARAMETER_NAME = 'ENABLE_UNIFIED_STOCK_VALIDATION';

3. 监控恢复：
   - 观察负库存是否继续增加
   - 检查发药功能是否正常

===============================================================================
十一、长期改进建议
===============================================================================

11.1 统一库存管理接口
------------------
建议创建统一的库存操作类，所有库存变动都通过该接口：
- 统一的库存验证逻辑
- 统一的安全检查机制
- 统一的日志记录功能
- 统一的异常处理机制

11.2 实时库存监控
---------------
建议建立实时监控系统：
- 监控负库存的产生
- 监控库存异常变动
- 自动报警机制
- 定期库存盘点

11.3 数据库约束优化
----------------
建议在数据库层面添加约束：
- 检查约束防止负库存
- 触发器记录库存变动
- 审计表记录操作历史

===============================================================================
报告结束
===============================================================================

本报告详细记录了药品库存负数问题的分析过程、修复方案和实施指南。
所有修改都经过仔细分析，确保在修复问题的同时不影响系统的正常运行。

如有疑问或需要技术支持，请及时联系开发团队。
